{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\monitor\\leakyRecord.vue?vue&type=template&id=f8fdff14&scoped=true", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\monitor\\leakyRecord.vue", "mtime": 1755682849078}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1751956544249}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}