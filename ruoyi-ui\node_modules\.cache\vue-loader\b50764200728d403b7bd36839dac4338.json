{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\monitor\\leakyRecord.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\monitor\\leakyRecord.vue", "mtime": 1755682849078}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751956526634}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGJhdGNoR2VuZXJhdGVSZXBvcnQsIGdldExpc3RXaXRoRGV0YWlscywgZ2V0UmVwb3J0TGlzdCB9IGZyb20gJ0AvYXBpL3NhZmUvbW9uaXRvcicKaW1wb3J0IFF1ZXN0UmVzdWx0RGV0YWlscyBmcm9tICcuLi8uLi9zYWZlL3NlcnZlci9xdWVzdFJlc3VsdERldGFpbHMnCmltcG9ydCBMZWFrU2NhbkRpYWxvZyBmcm9tICcuLi8uLi9zYWZlL3NlcnZlci9jb21wb25lbnRzL0xlYWtTY2FuRGlhbG9nJwppbXBvcnQgRmZKb2JUYXNrcyBmcm9tICcuL2ZmSm9iVGFza3MnCmltcG9ydCBKb2JMb2cgZnJvbSAnLi4vLi4vbW9uaXRvci9qb2IvbG9nJwppbXBvcnQgeyB0YXNrQ3JlYXRlcmVwb3J0LCB0YXNrRG93blJlcG9ydCB9IGZyb20gJ0AvYXBpL21vbml0b3IyL3dwcmVzdWx0JwoKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdIb3N0TGVha3lSZWNvcmQnLAogIGNvbXBvbmVudHM6IHsgSm9iTG9nLCBGZkpvYlRhc2tzLCBMZWFrU2NhbkRpYWxvZywgUXVlc3RSZXN1bHREZXRhaWxzIH0sCiAgZGljdHM6IFsnc3lzX2pvYl9ncm91cCcsICdzeXNfam9iX3N0YXR1cyddLAogIHByb3BzOiB7CiAgICB0b1BhcmFtczogewogICAgICB0eXBlOiBPYmplY3QsCiAgICAgIGRlZmF1bHQ6ICgpID0+IHt9CiAgICB9LAogICAgbGlzdFR5cGU6IHsKICAgICAgdHlwZTogTnVtYmVyLAogICAgICBkZWZhdWx0OiA0CiAgICB9CiAgfSwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgam9iVHlwZTogdW5kZWZpbmVkLAogICAgICAvLyDmmK/lkKbmmL7npLpDcm9u6KGo6L6+5byP5by55Ye65bGCCiAgICAgIG9wZW5Dcm9uOiBmYWxzZSwKICAgICAgLy8g5bGV56S65pyA6L+R5LiA5qyh6L+Q6KGM57uT5p6cCiAgICAgIC8vIOmBrue9qeWxggogICAgICBsb2FkaW5nOiB0cnVlLAogICAgICAvLyDku7vliqFJRAogICAgICBqb2JJZDogJycsCiAgICAgIHRvdGFsU2NhbjogMCwKICAgICAgLy8g6YCJ5Lit5pWw57uECiAgICAgIGlkczogW10sCiAgICAgIC8vIOmdnuWNleS4quemgeeUqAogICAgICBzaW5nbGU6IHRydWUsCiAgICAgIC8vIOmdnuWkmuS4quemgeeUqAogICAgICBtdWx0aXBsZTogdHJ1ZSwKICAgICAgLy8g5pi+56S65pCc57Si5p2h5Lu2CiAgICAgIHNob3dTZWFyY2g6IHRydWUsCiAgICAgIC8vIOaAu+adoeaVsAogICAgICB0b3RhbDogMCwKICAgICAgLy8g5a6a5pe25Lu75Yqh6KGo5qC85pWw5o2uCiAgICAgIGpvYkxpc3Q6IFtdLAoKICAgICAgLy8g5piv5ZCm5pi+56S65by55Ye65bGCCiAgICAgIG9wZW46IGZhbHNlLAogICAgICAvLyDmmK/lkKbmmL7npLror6bnu4blvLnlh7rlsYIKICAgICAgb3BlblZpZXc6IGZhbHNlLAogICAgICBlZGl0Rm9ybToge30sCiAgICAgIC8vIOafpeivouWPguaVsAogICAgICBxdWVyeVBhcmFtczogewogICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgcGFnZVNpemU6IDEwCiAgICAgIH0sCiAgICAgIGlzRGlzYWJsZWQ6IGZhbHNlLAogICAgICAvLyDlkajmnJ/ovazmjaLmloflrZcKICAgICAgY3JvblRleHQ6ICcnLAogICAgICByb3dzOiBbXSwKICAgICAgZ2V0TGlzdEludGVydmFsOiBudWxsLAogICAgICAvLyDmiqXlkYrnlJ/miJDorrDlvZXnm7jlhbPmlbDmja4KICAgICAgcmVwb3J0UmVjb3JkRGlhbG9nVmlzaWJsZTogZmFsc2UsCiAgICAgIHJlcG9ydExvYWRpbmc6IGZhbHNlLAogICAgICByZXBvcnRMaXN0OiBbXSwKICAgICAgcmVwb3J0VG90YWw6IDAsCiAgICAgIHJlcG9ydFF1ZXJ5UGFyYW1zOiB7CiAgICAgICAgcGFnZU51bTogMSwKICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgICAgdGFza1R5cGU6IHVuZGVmaW5lZAogICAgICB9LAogICAgICBzZWxlY3RlZElkczogW10KICAgIH0KICB9LAogIHdhdGNoOiB7CiAgICB0b1BhcmFtczogewogICAgICBoYW5kbGVyKG5ld1ZhbCkgewogICAgICAgIGlmIChuZXdWYWwgJiYgbmV3VmFsLmlkKSB7CiAgICAgICAgICB0aGlzLmhhbmRsZUpvYkxvZyh7CiAgICAgICAgICAgIGpvYklkOiBuZXdWYWwuaWQKICAgICAgICAgIH0pCiAgICAgICAgfQogICAgICB9LAogICAgICBpbW1lZGlhdGU6IHRydWUKICAgIH0KICB9LAogIGNyZWF0ZWQoKSB7CiAgICB0aGlzLmdldExpc3QoKQogICAgdGhpcy5nZXRMaXN0SW50ZXJ2YWwgPSBzZXRJbnRlcnZhbCgoKSA9PiB7CiAgICAgIHRoaXMubG9vcEdldExpc3QoKQogICAgfSwgMTAwMDApCiAgfSwKICBkZXN0cm95ZWQoKSB7CiAgICBpZiAodGhpcy5nZXRMaXN0SW50ZXJ2YWwpIHsKICAgICAgY2xlYXJJbnRlcnZhbCh0aGlzLmdldExpc3RJbnRlcnZhbCkKICAgIH0KICB9LAogIG1ldGhvZHM6IHsKICAgIC8qKiDmn6Xor6LkuLvmnLrmvI/miavorrDlvZXliJfooaggKi8KICAgIGdldExpc3QoKSB7CiAgICAgIHRoaXMubG9hZGluZyA9IHRydWUKICAgICAgdGhpcy5xdWVyeVBhcmFtcy50YXNrVHlwZSA9IHRoaXMubGlzdFR5cGUgPT09IDQgPyAyIDogMQogICAgICBnZXRMaXN0V2l0aERldGFpbHModGhpcy5xdWVyeVBhcmFtcykudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgdGhpcy5qb2JMaXN0ID0gcmVzcG9uc2Uucm93cwogICAgICAgIHRoaXMudG90YWwgPSByZXNwb25zZS50b3RhbAogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlCiAgICAgIH0pCiAgICB9LAogICAgLyoqIOafpeivouWumuaXtuS7u+WKoeWIl+ihqCAqLwogICAgbG9vcEdldExpc3QoKSB7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMudGFza1R5cGUgPSB0aGlzLmxpc3RUeXBlID09PSA0ID8gMiA6IDEKICAgICAgZ2V0TGlzdFdpdGhEZXRhaWxzKHRoaXMucXVlcnlQYXJhbXMpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIGNvbnN0IG5ld0pvYkxpc3QgPSByZXNwb25zZS5yb3dzCiAgICAgICAgY29uc3Qgc2VsZWN0ZWRJZHMgPSBbLi4udGhpcy5zZWxlY3RlZElkc10gLy8g5L+d5a2Y5b2T5YmN6YCJ5Lit55qESUQKCiAgICAgICAgdGhpcy5qb2JMaXN0ID0gbmV3Sm9iTGlzdAogICAgICAgIHRoaXMudG90YWwgPSByZXNwb25zZS50b3RhbAoKICAgICAgICAvLyDlnKhET03mm7TmlrDlkI7mgaLlpI3pgInkuK3nirbmgIEKICAgICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7CiAgICAgICAgICAvLyDmn6Xmib7pnIDopoHph43mlrDpgInkuK3nmoTooYwKICAgICAgICAgIGNvbnN0IHJvd3NUb1NlbGVjdCA9IHRoaXMuam9iTGlzdC5maWx0ZXIocm93ID0+CiAgICAgICAgICAgIHNlbGVjdGVkSWRzLmluY2x1ZGVzKHJvdy5pZCkKICAgICAgICAgICkKCiAgICAgICAgICAvLyDph43mlrDpgInkuK3kuYvliY3pgInkuK3nmoTpobkKICAgICAgICAgIHRoaXMuJHJlZnMubXVsdGlwbGVUYWJsZS5jbGVhclNlbGVjdGlvbigpCiAgICAgICAgICByb3dzVG9TZWxlY3QuZm9yRWFjaChyb3cgPT4gewogICAgICAgICAgICB0aGlzLiRyZWZzLm11bHRpcGxlVGFibGUudG9nZ2xlUm93U2VsZWN0aW9uKHJvdywgdHJ1ZSkKICAgICAgICAgIH0pCiAgICAgICAgfSkKICAgICAgfSkKICAgIH0sCgogICAgLyoqIOaQnOe0ouaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlUXVlcnkoKSB7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDEKICAgICAgdGhpcy5nZXRMaXN0KCkKICAgIH0sCiAgICAvKiog6YeN572u5oyJ6ZKu5pON5L2cICovCiAgICByZXNldFF1ZXJ5KCkgewogICAgICB0aGlzLnJlc2V0Rm9ybSgncXVlcnlGb3JtJykKICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpCiAgICB9LAoKICAgIGdldFJvd0tleShyb3cpIHsKICAgICAgcmV0dXJuIHJvdy5pZCAvLyDkvb/nlKggam9iSWQg5L2c5Li66KGM55qE5ZSv5LiA5qCH6K+GCiAgICB9LAoKICAgIC8vIOWkmumAieahhumAieS4reaVsOaNrgogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKHNlbGVjdGlvbikgewogICAgICB0aGlzLmlkcyA9IHNlbGVjdGlvbi5tYXAoaXRlbSA9PiBpdGVtLmlkKQogICAgICB0aGlzLnNpbmdsZSA9IHNlbGVjdGlvbi5sZW5ndGggIT0gMQogICAgICB0aGlzLm11bHRpcGxlID0gIXNlbGVjdGlvbi5sZW5ndGgKICAgICAgdGhpcy5yb3dzID0gc2VsZWN0aW9uCiAgICAgIHRoaXMuc2VsZWN0ZWRJZHMgPSBbLi4udGhpcy5pZHNdIC8vIOS/ruWkje+8muWOn+adpeaYryB0aGlzLmlkCiAgICB9LAoKICAgIC8qKiDnlJ/miJDmiqXlkYogKi8KICAgIGhhbmRsZUNyZWF0ZVJlcG9ydChyb3cpIHsKICAgICAgdGFza0NyZWF0ZXJlcG9ydChyb3cpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIHRoaXMuZ2V0TGlzdCgpCiAgICAgIH0pCiAgICB9LAoKICAgIC8qKiDmibnph4/nlJ/miJDmiqXlkYogKi8KICAgIGJhdGNoQ3JlYXRlUmVwb3J0KHJvdykgewogICAgICAvLyDmibnph4/nlJ/miJDmiqXlkYoKICAgICAgaWYgKHRoaXMucm93cy5sZW5ndGggPT09IDApIHsKICAgICAgICB0aGlzLiRtb2RhbC5tc2dXYXJuaW5nKCfor7flhYjpgInmi6nopoHnlJ/miJDmiqXlkYrnmoTorrDlvZUnKQogICAgICAgIHJldHVybgogICAgICB9CiAgICAgIGNvbnN0IGpvYklkcyA9IHRoaXMucm93cy5tYXAoaXRlbSA9PiBpdGVtLmlkKQogICAgICBiYXRjaEdlbmVyYXRlUmVwb3J0KHsKICAgICAgICBpZHM6IGpvYklkcywKICAgICAgICB0YXNrVHlwZTogdGhpcy5saXN0VHlwZSA9PT0gNCA/IDIgOiAxCiAgICAgIH0pLnRoZW4ocmVzID0+IHsKICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCfmibnph4/miqXlkYrnlJ/miJDku7vliqHlt7Lmj5DkuqQnKQogICAgICB9KQogICAgfSwKCiAgICAvKiog5oql5ZGK55Sf5oiQ6K6w5b2VICovCiAgICBoYW5kbGVSZXBvcnRSZWNvcmQoKSB7CiAgICAgIHRoaXMucmVwb3J0UmVjb3JkRGlhbG9nVmlzaWJsZSA9IHRydWUKICAgICAgdGhpcy5yZXBvcnRRdWVyeVBhcmFtcy50YXNrVHlwZSA9IHRoaXMubGlzdFR5cGUgPT09IDQgPyAyIDogMQogICAgICB0aGlzLmdldFJlcG9ydExpc3QoKQogICAgfSwKCiAgICAvKiog6I635Y+W5oql5ZGK55Sf5oiQ6K6w5b2V5YiX6KGoICovCiAgICBnZXRSZXBvcnRMaXN0KCkgewogICAgICB0aGlzLnJlcG9ydExvYWRpbmcgPSB0cnVlCiAgICAgIGdldFJlcG9ydExpc3QodGhpcy5yZXBvcnRRdWVyeVBhcmFtcykudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgdGhpcy5yZXBvcnRMaXN0ID0gcmVzcG9uc2Uucm93cwogICAgICAgIHRoaXMucmVwb3J0VG90YWwgPSByZXNwb25zZS50b3RhbAogICAgICAgIHRoaXMucmVwb3J0TG9hZGluZyA9IGZhbHNlCiAgICAgIH0pLmNhdGNoKCgpID0+IHsKICAgICAgICB0aGlzLnJlcG9ydExvYWRpbmcgPSBmYWxzZQogICAgICB9KQogICAgfSwKCiAgICBkb3duUmVwb3J0KHJvdykgewogICAgICB0YXNrRG93blJlcG9ydChyb3cpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIGlmIChyZXNwb25zZS5jb2RlID09PSAyMDApIHsKICAgICAgICAgIHdpbmRvdy5vcGVuKHJlc3BvbnNlLm1zZywgJ19ibGFuaycpCiAgICAgICAgfQogICAgICB9KQogICAgfSwKCiAgICAvKiog5Lu75Yqh6K+m57uG5L+h5oGvICovCiAgICBoYW5kbGVWaWV3KHJvdykgewogICAgICB0aGlzLm9wZW5WaWV3ID0gdHJ1ZQogICAgICB0aGlzLmpvYlR5cGUgPSAyCiAgICAgIHRoaXMuam9iSWQgPSByb3cuam9iSWQKICAgICAgdGhpcy5lZGl0Rm9ybSA9IHsgLi4ucm93IH0KICAgIH0KICB9Cn0K"}, {"version": 3, "sources": ["leakyRecord.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiNA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "leakyRecord.vue", "sourceRoot": "src/views/frailty/monitor", "sourcesContent": ["<template>\n  <div class=\"custom-container\">\n    <div class=\"custom-content-container-right\">\n      <div class=\"custom-content-search-box\">\n        <el-form\n          ref=\"queryForm\"\n          :model=\"queryParams\"\n          size=\"small\"\n          :inline=\"true\"\n          label-position=\"right\"\n          label-width=\"70px\"\n        >\n          <el-row :gutter=\"10\">\n            <el-col :span=\"6\">\n              <el-form-item label=\"任务名称\" prop=\"jobName\">\n                <el-input\n                  v-model=\"queryParams.jobName\"\n                  placeholder=\"请输入任务名称\"\n                  clearable\n                  @keyup.enter.native=\"handleQuery\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item label=\"扫描目标\" prop=\"scanTarget\">\n                <el-input\n                  v-model=\"queryParams.scanTarget\"\n                  placeholder=\"扫描目标\"\n                  clearable\n                  @keyup.enter.native=\"handleQuery\"\n                />\n              </el-form-item>\n            </el-col>\n            <!--            <el-col :span=\"6\">\n              <el-form-item label=\"任务状态\" prop=\"status\">\n                <el-select v-model=\"queryParams.status\" placeholder=\"请选择任务状态\" clearable>\n                  <el-option\n                    v-for=\"dict in dict.type.sys_job_status\"\n                    :key=\"dict.value\"\n                    :label=\"dict.label\"\n                    :value=\"dict.value\"\n                  />\n                </el-select>\n              </el-form-item>\n            </el-col>-->\n            <el-col :span=\"12\">\n              <el-form-item class=\"custom-search-btn\">\n                <el-button class=\"btn1\" size=\"small\" @click=\"handleQuery\">查询</el-button>\n                <el-button class=\"btn2\" size=\"small\" @click=\"resetQuery\">重置</el-button>\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </el-form>\n      </div>\n\n      <div class=\"custom-content-container\">\n        <div class=\"common-header\">\n          <div><span class=\"common-head-title\">{{ listType === 4 ? '主机' : 'Web' }}漏扫记录列表</span></div>\n          <div class=\"common-head-right\">\n            <el-row :gutter=\"10\">\n              <el-col :span=\"1.5\">\n                <el-button\n                  type=\"primary\"\n                  size=\"small\"\n                  @click=\"handleReportRecord\"\n                >报告生成记录\n                </el-button>\n              </el-col>\n              <el-col :span=\"1.5\">\n                <el-button\n                  class=\"btn1\"\n                  size=\"small\"\n                  :disabled=\"multiple\"\n                  @click=\"batchCreateReport\"\n                >批量生成报告\n                </el-button>\n              </el-col>\n            </el-row>\n          </div>\n        </div>\n        <el-table\n          ref=\"multipleTable\"\n          v-loading=\"loading\"\n          height=\"100%\"\n          :data=\"jobList\"\n          :row-key=\"getRowKey\"\n          @selection-change=\"handleSelectionChange\"\n        >\n          <el-table-column type=\"selection\" width=\"55\" />\n          <el-table-column label=\"任务名称\" align=\"left\" prop=\"jobName\" />\n          <el-table-column label=\"扫描目标\" align=\"left\" prop=\"scanTarget\" width=\"150px\" :show-overflow-tooltip=\"false\" />\n          <el-table-column label=\"扫描状态\" align=\"left\" prop=\"taskStatus\">\n            <template slot-scope=\"scope\">\n              <el-tag v-if=\"scope.row.taskStatus === 1\">正在调度</el-tag>\n              <el-tag v-else-if=\"scope.row.taskStatus === 2\" type=\"primary\">运行中</el-tag>\n              <el-tag v-else-if=\"scope.row.taskStatus === 3\" type=\"danger\">任务异常</el-tag>\n              <el-tag v-else-if=\"scope.row.taskStatus === 4\" type=\"success\">扫描完成</el-tag>\n            </template>\n          </el-table-column>\n          <el-table-column\n            label=\"扫描进度\"\n            prop=\"finishRate\"\n            width=\"120\"\n            align=\"left\"\n          >\n            <template slot-scope=\"scope\">\n              <el-progress :text-inside=\"true\" :stroke-width=\"18\" :percentage=\"scope.row.finishRate\" />\n            </template>\n          </el-table-column>\n          <el-table-column v-if=\"listType === 4\" label=\"存活主机\" align=\"left\" prop=\"hostNum\" />\n          <el-table-column v-if=\"listType === 4\" label=\"弱口令\" align=\"left\" prop=\"pwNum\" />\n          <el-table-column label=\"可入侵漏洞\" align=\"left\" prop=\"pocRiskNum\" />\n          <el-table-column label=\"高危漏洞\" align=\"left\" prop=\"highRiskNum\" />\n          <el-table-column label=\"中危漏洞\" align=\"left\" prop=\"lowRiskNum\" />\n          <el-table-column label=\"低危漏洞\" align=\"left\" prop=\"lowRiskNum\" />\n          <el-table-column label=\"开始时间\" align=\"left\" prop=\"startTime\" />\n          <el-table-column label=\"结束时间\" align=\"left\" prop=\"endTime\" />\n          <el-table-column label=\"操作\" width=\"150\" fixed=\"right\" :show-overflow-tooltip=\"false\">\n            <template slot-scope=\"scope\">\n              <el-button\n                v-hasPermi=\"['monitor:ipschedule:query']\"\n                size=\"mini\"\n                type=\"text\"\n                @click=\"handleView(scope.row)\"\n              >详情\n              </el-button>\n              <el-button\n                v-if=\"listType === 5 && scope.row.taskStatus === 2 && scope.row.reportStatus === null\"\n                size=\"mini\"\n                type=\"text\"\n                @click=\"handleCreateReport(scope.row)\"\n              >生成报告\n              </el-button>\n              <el-button\n                v-if=\"listType === 4 && scope.row.taskStatus === 4 && scope.row.reportStatus === null\"\n                size=\"mini\"\n                type=\"text\"\n                @click=\"handleCreateReport(scope.row)\"\n              >生成报告\n              </el-button>\n              <el-button\n                v-if=\"scope.row.reportStatus !== null && scope.row.reportStatus !== 2\"\n                size=\"mini\"\n                type=\"text\"\n              >报告生成中\n              </el-button>\n              <el-button\n                v-if=\"scope.row.reportStatus === 2\"\n                size=\"mini\"\n                type=\"text\"\n                @click=\"downReport(scope.row)\"\n              >下载报告\n              </el-button>\n            </template>\n          </el-table-column>\n        </el-table>\n        <pagination\n          v-show=\"total>0\"\n          :total=\"total\"\n          :page.sync=\"queryParams.pageNum\"\n          :limit.sync=\"queryParams.pageSize\"\n          @pagination=\"getList\"\n        />\n      </div>\n    </div>\n\n    <el-dialog title=\"报告生成记录\" :visible.sync=\"reportRecordDialogVisible\" width=\"80%\" append-to-body>\n      <div class=\"custom-content-container\">\n        <el-table ref=\"reportTable\" v-loading=\"reportLoading\" height=\"100%\" :data=\"reportList\">\n          <el-table-column label=\"扫描目标\" align=\"left\" prop=\"scanTarget\" />\n          <el-table-column label=\"创建时间\" align=\"left\" prop=\"createTime\" />\n          <el-table-column label=\"生成时间\" align=\"left\" prop=\"generateTime\" />\n          <el-table-column label=\"生成状态\" align=\"left\" prop=\"reportStatus\">\n            <template slot-scope=\"scope\">\n              <el-tag v-if=\"scope.row.reportStatus === 0\" type=\"primary\">正在生成</el-tag>\n              <el-tag v-else-if=\"scope.row.reportStatus === 1\" type=\"primary\">正在生成</el-tag>\n              <el-tag v-else-if=\"scope.row.reportStatus === 2\" type=\"success\">已完成</el-tag>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"操作\" width=\"150\" fixed=\"right\">\n            <template slot-scope=\"scope\">\n              <el-button\n                size=\"mini\"\n                type=\"text\"\n                :disabled=\"scope.row.reportStatus !== 2\"\n                @click=\"downReport(scope.row)\"\n              >下载\n              </el-button>\n            </template>\n          </el-table-column>\n        </el-table>\n        <pagination\n          v-show=\"reportTotal>0\"\n          :total=\"reportTotal\"\n          :page.sync=\"reportQueryParams.pageNum\"\n          :limit.sync=\"reportQueryParams.pageSize\"\n          @pagination=\"getReportList\"\n        />\n      </div>\n    </el-dialog>\n\n    <!-- 任务日志详细 -->\n    <el-dialog v-if=\"openView\" v-dialog-drag title=\"任务详细\" :visible.sync=\"openView\" width=\"1200px\" append-to-body>\n      <ff-job-tasks v-if=\"openView\" :job-id=\"jobId\" :job-type=\"jobType\" :job-row=\"editForm\" />\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { batchGenerateReport, getListWithDetails, getReportList } from '@/api/safe/monitor'\nimport QuestResultDetails from '../../safe/server/questResultDetails'\nimport LeakScanDialog from '../../safe/server/components/LeakScanDialog'\nimport FfJobTasks from './ffJobTasks'\nimport JobLog from '../../monitor/job/log'\nimport { taskCreatereport, taskDownReport } from '@/api/monitor2/wpresult'\n\nexport default {\n  name: 'HostLeakyRecord',\n  components: { JobLog, FfJobTasks, LeakScanDialog, QuestResultDetails },\n  dicts: ['sys_job_group', 'sys_job_status'],\n  props: {\n    toParams: {\n      type: Object,\n      default: () => {}\n    },\n    listType: {\n      type: Number,\n      default: 4\n    }\n  },\n  data() {\n    return {\n      jobType: undefined,\n      // 是否显示Cron表达式弹出层\n      openCron: false,\n      // 展示最近一次运行结果\n      // 遮罩层\n      loading: true,\n      // 任务ID\n      jobId: '',\n      totalScan: 0,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 定时任务表格数据\n      jobList: [],\n\n      // 是否显示弹出层\n      open: false,\n      // 是否显示详细弹出层\n      openView: false,\n      editForm: {},\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10\n      },\n      isDisabled: false,\n      // 周期转换文字\n      cronText: '',\n      rows: [],\n      getListInterval: null,\n      // 报告生成记录相关数据\n      reportRecordDialogVisible: false,\n      reportLoading: false,\n      reportList: [],\n      reportTotal: 0,\n      reportQueryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        taskType: undefined\n      },\n      selectedIds: []\n    }\n  },\n  watch: {\n    toParams: {\n      handler(newVal) {\n        if (newVal && newVal.id) {\n          this.handleJobLog({\n            jobId: newVal.id\n          })\n        }\n      },\n      immediate: true\n    }\n  },\n  created() {\n    this.getList()\n    this.getListInterval = setInterval(() => {\n      this.loopGetList()\n    }, 10000)\n  },\n  destroyed() {\n    if (this.getListInterval) {\n      clearInterval(this.getListInterval)\n    }\n  },\n  methods: {\n    /** 查询主机漏扫记录列表 */\n    getList() {\n      this.loading = true\n      this.queryParams.taskType = this.listType === 4 ? 2 : 1\n      getListWithDetails(this.queryParams).then(response => {\n        this.jobList = response.rows\n        this.total = response.total\n        this.loading = false\n      })\n    },\n    /** 查询定时任务列表 */\n    loopGetList() {\n      this.queryParams.taskType = this.listType === 4 ? 2 : 1\n      getListWithDetails(this.queryParams).then(response => {\n        const newJobList = response.rows\n        const selectedIds = [...this.selectedIds] // 保存当前选中的ID\n\n        this.jobList = newJobList\n        this.total = response.total\n\n        // 在DOM更新后恢复选中状态\n        this.$nextTick(() => {\n          // 查找需要重新选中的行\n          const rowsToSelect = this.jobList.filter(row =>\n            selectedIds.includes(row.id)\n          )\n\n          // 重新选中之前选中的项\n          this.$refs.multipleTable.clearSelection()\n          rowsToSelect.forEach(row => {\n            this.$refs.multipleTable.toggleRowSelection(row, true)\n          })\n        })\n      })\n    },\n\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1\n      this.getList()\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm('queryForm')\n      this.handleQuery()\n    },\n\n    getRowKey(row) {\n      return row.id // 使用 jobId 作为行的唯一标识\n    },\n\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.id)\n      this.single = selection.length != 1\n      this.multiple = !selection.length\n      this.rows = selection\n      this.selectedIds = [...this.ids] // 修复：原来是 this.id\n    },\n\n    /** 生成报告 */\n    handleCreateReport(row) {\n      taskCreatereport(row).then(response => {\n        this.getList()\n      })\n    },\n\n    /** 批量生成报告 */\n    batchCreateReport(row) {\n      // 批量生成报告\n      if (this.rows.length === 0) {\n        this.$modal.msgWarning('请先选择要生成报告的记录')\n        return\n      }\n      const jobIds = this.rows.map(item => item.id)\n      batchGenerateReport({\n        ids: jobIds,\n        taskType: this.listType === 4 ? 2 : 1\n      }).then(res => {\n        this.$modal.msgSuccess('批量报告生成任务已提交')\n      })\n    },\n\n    /** 报告生成记录 */\n    handleReportRecord() {\n      this.reportRecordDialogVisible = true\n      this.reportQueryParams.taskType = this.listType === 4 ? 2 : 1\n      this.getReportList()\n    },\n\n    /** 获取报告生成记录列表 */\n    getReportList() {\n      this.reportLoading = true\n      getReportList(this.reportQueryParams).then(response => {\n        this.reportList = response.rows\n        this.reportTotal = response.total\n        this.reportLoading = false\n      }).catch(() => {\n        this.reportLoading = false\n      })\n    },\n\n    downReport(row) {\n      taskDownReport(row).then(response => {\n        if (response.code === 200) {\n          window.open(response.msg, '_blank')\n        }\n      })\n    },\n\n    /** 任务详细信息 */\n    handleView(row) {\n      this.openView = true\n      this.jobType = 2\n      this.jobId = row.jobId\n      this.editForm = { ...row }\n    }\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n@import \"@/assets/styles/assetIndex.scss\";\n.policyCol {\n  min-width: 330px;\n  margin-top: 10px;\n}\n\n.policyDesc {\n  display: flex;\n  height: 80px;\n}\n\n.policyTxt {\n  margin-left: 10px;\n  line-height: 20px;\n}\n\n.policyTitle {\n  height: 40px;\n  line-height: 40px;\n}\n\n.oneLine {\n  overflow: hidden;\n  white-space: nowrap;\n  text-overflow: ellipsis;\n}\n\n::v-deep .el-table {\n  display: flex;\n  flex-direction: column;\n}\n\n::v-deep .el-table__body-wrapper {\n  overflow-y: auto;\n  flex: 1;\n}\n</style>\n"]}]}